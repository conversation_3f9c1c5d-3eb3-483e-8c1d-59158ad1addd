{% comment %}
Form Builder Scripts Component
Initializes the Vue.js form builder application with proper data and configuration.
Integrates with Vite build system and maintains compatibility with Django template inheritance.
{% endcomment %}

{% load static %}
{% load vite_tags %}

<!-- Form Builder Data Initialization -->
<script>
  // Initialize global form builder data for Vue.js application
  window.formBuilderData = {
    // Form data from Django context
    formData: {
      // Basic form information
      id: '{{ form.id }}',
      name: '{{ form.name|escapejs }}',
      description: '{{ form.description|escapejs }}',
      slug: '{{ form.slug|escapejs }}',
      status: '{{ form.status|escapejs }}',
      
      // Form fields (parsed from JSON)
      fields: {{ form_fields_json|safe }},
      
      // Form settings (parsed from JSON)
      settings: {{ form_settings_json|safe }},
      
      // Form customization (parsed from JSON)
      customization: {{ form_customization_json|safe }}
    },
    
    // Configuration for the form builder
    config: {
      formSlug: '{{ form.slug|escapejs }}',
      apiBaseUrl: '/api/forms/',
      csrfToken: '{{ csrf_token }}',
      
      // API endpoints
      endpoints: {
        formUpdate: '/api/forms/{{ form.slug }}/',
        fieldCreate: '/api/forms/{{ form.slug }}/fields/',
        fieldUpdate: '/api/forms/{{ form.slug }}/fields/{id}/',
        fieldDelete: '/api/forms/{{ form.slug }}/fields/{id}/',
        fieldReorder: '/api/forms/{{ form.slug }}/fields/reorder/'
      },
      
      // Feature flags
      features: {
        autosave: true,
        autosaveInterval: 30000, // 30 seconds
        undoRedo: true,
        keyboardShortcuts: true,
        dragAndDrop: true,
        realTimePreview: true
      },
      
      // UI configuration
      ui: {
        sidebarWidth: 320,
        propertiesWidth: 320,
        minCanvasWidth: 400,
        maxUndoSteps: 50
      }
    },
    
    // CSRF token for API requests
    csrfToken: '{{ csrf_token }}'
  };
  
  // Debug logging in development
  {% if debug %}
  console.log('Form Builder Data Initialized:', window.formBuilderData);
  {% endif %}
</script>

<!-- Load Vite Assets for Form Builder -->
{% load_vite_assets 'form-builder' %}

<!-- Form Builder Initialization Script -->
<script type="module">
  // Wait for DOM to be ready
  document.addEventListener('DOMContentLoaded', function() {
    // Check if Vue.js is available
    if (typeof Vue === 'undefined') {
      console.error('Vue.js is not loaded. Form builder cannot initialize.');
      
      // Show fallback content
      const fallbackContent = document.getElementById('fallback-content');
      const formBuilderApp = document.getElementById('form-builder-app');
      
      if (fallbackContent && formBuilderApp) {
        formBuilderApp.style.display = 'none';
        fallbackContent.classList.remove('hidden');
      }
      return;
    }
    
    // Check if form builder data is available
    if (!window.formBuilderData) {
      console.error('Form builder data not found. Cannot initialize form builder.');
      return;
    }
    
    // Initialize the form builder application
    try {
      console.log('Initializing Form Builder Vue.js Application...');
      
      // Import and initialize the form builder if using ES modules
      if (window.initFormBuilder && typeof window.initFormBuilder === 'function') {
        const app = window.initFormBuilder(
          '#form-builder-app',
          window.formBuilderData.formData,
          window.formBuilderData.csrfToken
        );
        
        console.log('Form Builder initialized successfully:', app);
      } else {
        console.warn('Form builder initialization function not found. Using fallback initialization.');
        
        // Fallback initialization for development
        const { createApp } = Vue;
        
        const app = createApp({
          data() {
            return {
              activeTab: 'fields',
              formData: window.formBuilderData.formData,
              availableFieldTypes: [
                {
                  id: 'text',
                  name: 'Text Input',
                  description: 'Single line text input',
                  icon: '📝',
                  color: 'blue'
                },
                {
                  id: 'textarea',
                  name: 'Text Area',
                  description: 'Multi-line text input',
                  icon: '📄',
                  color: 'green'
                },
                {
                  id: 'email',
                  name: 'Email',
                  description: 'Email address input',
                  icon: '📧',
                  color: 'purple'
                },
                {
                  id: 'select',
                  name: 'Dropdown',
                  description: 'Dropdown selection',
                  icon: '📋',
                  color: 'orange'
                }
              ]
            };
          },
          methods: {
            addField(fieldType) {
              console.log('Adding field:', fieldType);
              // Placeholder for field addition logic
            }
          },
          mounted() {
            console.log('Form Builder Vue app mounted successfully');
            console.log('Form data:', this.formData);
          }
        });
        
        app.mount('#form-builder-app');
        console.log('Fallback Form Builder initialized');
      }
      
    } catch (error) {
      console.error('Error initializing form builder:', error);
      
      // Show fallback content on error
      const fallbackContent = document.getElementById('fallback-content');
      const formBuilderApp = document.getElementById('form-builder-app');
      
      if (fallbackContent && formBuilderApp) {
        formBuilderApp.style.display = 'none';
        fallbackContent.classList.remove('hidden');
      }
    }
  });
</script>

<!-- Additional Form Builder Utilities -->
<script>
  // Global utilities for form builder
  window.FormBuilderUtils = {
    // Generate unique field ID
    generateFieldId() {
      return 'field_' + Math.random().toString(36).substr(2, 9);
    },
    
    // Validate field configuration
    validateField(field) {
      if (!field.name || !field.type) {
        return false;
      }
      return true;
    },
    
    // Format field for API
    formatFieldForAPI(field) {
      return {
        name: field.name,
        label: field.label || field.name,
        field_type: field.type,
        required: field.required || false,
        placeholder: field.placeholder || '',
        help_text: field.help_text || '',
        default_value: field.default_value || '',
        order: field.order || 0,
        is_visible: field.is_visible !== false,
        properties: field.properties || {},
        validation_rules: field.validation_rules || {},
        conditional_logic: field.conditional_logic || {}
      };
    },
    
    // Setup CSRF for AJAX requests
    setupCSRF() {
      const csrfToken = window.formBuilderData?.csrfToken;
      if (csrfToken && window.axios) {
        window.axios.defaults.headers.common['X-CSRFToken'] = csrfToken;
      }
    }
  };
  
  // Initialize CSRF setup
  window.FormBuilderUtils.setupCSRF();
  
  {% if debug %}
  console.log('Form Builder utilities loaded:', window.FormBuilderUtils);
  {% endif %}
</script>

{% comment %}
Form Builder Scripts Component
Initializes the Vue.js form builder application with proper data and configuration.
Integrates with Vite build system and maintains compatibility with Django template inheritance.
{% endcomment %}

{% load static %}
{% load vite_tags %}

<!-- Form Builder Data Initialization -->
<script>
  // Initialize global form builder data for Vue.js application
  window.formBuilderData = {
    // Form data from Django context
    formData: {
      // Basic form information
      id: '{{ form.id }}',
      name: '{{ form.name|escapejs }}',
      description: '{{ form.description|escapejs }}',
      slug: '{{ form.slug|escapejs }}',
      status: '{{ form.status|escapejs }}',
      
      // Form fields (parsed from JSON)
      fields: {{ form_fields_json|safe }},
      
      // Form settings (parsed from JSON)
      settings: {{ form_settings_json|safe }},
      
      // Form customization (parsed from JSON)
      customization: {{ form_customization_json|safe }}
    },
    
    // Configuration for the form builder
    config: {
      formSlug: '{{ form.slug|escapejs }}',
      apiBaseUrl: '/api/forms/',
      csrfToken: '{{ csrf_token }}',
      
      // API endpoints
      endpoints: {
        formUpdate: '/api/forms/{{ form.slug }}/',
        fieldCreate: '/api/forms/{{ form.slug }}/fields/',
        fieldUpdate: '/api/forms/{{ form.slug }}/fields/{id}/',
        fieldDelete: '/api/forms/{{ form.slug }}/fields/{id}/',
        fieldReorder: '/api/forms/{{ form.slug }}/fields/reorder/'
      },
      
      // Feature flags
      features: {
        autosave: true,
        autosaveInterval: 30000, // 30 seconds
        undoRedo: true,
        keyboardShortcuts: true,
        dragAndDrop: true,
        realTimePreview: true
      },
      
      // UI configuration
      ui: {
        sidebarWidth: 320,
        propertiesWidth: 320,
        minCanvasWidth: 400,
        maxUndoSteps: 50
      }
    },
    
    // CSRF token for API requests
    csrfToken: '{{ csrf_token }}'
  };
  
  // Debug logging in development
  {% if debug %}
  console.log('Form Builder Data Initialized:', window.formBuilderData);
  {% endif %}
</script>

<!-- Form Builder Loading Styles -->
<style>
  /* Prevent flickering during initialization */
  #form-builder-app {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  #form-builder-app.initialized {
    opacity: 1;
  }

  /* Loading state */
  .form-builder-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    background-color: #f9fafb;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Ensure proper visibility */
  #form-builder-app.vue-mounted {
    visibility: visible !important;
    opacity: 1 !important;
  }
</style>

<!-- Load Vite Assets for Form Builder -->
{% load_vite_assets 'form-builder' %}

<!-- Form Builder Initialization Script -->
<script type="module">
  // Prevent multiple initialization attempts
  if (window.formBuilderInitialized) {
    console.log('Form builder already initialized, skipping...');
    return;
  }

  // Mark as initialized to prevent conflicts
  window.formBuilderInitialized = true;

  // Set up a success callback for when the Vite form builder initializes
  window.onFormBuilderReady = function() {
    console.log('Form builder ready callback triggered');
    const formBuilderElement = document.getElementById('form-builder-app');
    const loadingElement = document.getElementById('form-builder-loading');

    if (formBuilderElement) {
      formBuilderElement.classList.add('initialized', 'vue-mounted');
      console.log('Form builder visibility enabled');
    }

    if (loadingElement) {
      loadingElement.style.display = 'none';
      console.log('Loading state hidden');
    }
  };

  // Wait for both DOM and Vite assets to be ready
  let initAttempts = 0;
  const maxAttempts = 100; // 10 seconds max wait

  function attemptInitialization() {
    initAttempts++;

    // Check if we've exceeded max attempts
    if (initAttempts > maxAttempts) {
      console.error('Form builder initialization timeout. Showing fallback.');
      showFallbackContent();
      return;
    }

    // Check if the Vite-built form builder is available
    if (window.initFormBuilder && typeof window.initFormBuilder === 'function') {
      console.log('Vite form builder found, letting it handle initialization...');

      // The Vite-built form builder will handle initialization
      // We just need to ensure our data is available
      if (!window.formBuilderData) {
        console.error('Form builder data not found. Cannot initialize form builder.');
        showFallbackContent();
        return;
      }

      // Don't interfere with Vite initialization, just wait for success
      console.log('Form builder data ready, waiting for Vite initialization...');

      // Use MutationObserver to detect when Vue app content changes
      const formBuilderElement = document.getElementById('form-builder-app');
      if (formBuilderElement) {
        const observer = new MutationObserver((mutations) => {
          for (const mutation of mutations) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
              // Check if Vue-specific content was added
              for (const node of mutation.addedNodes) {
                if (node.nodeType === Node.ELEMENT_NODE && (
                  node.hasAttribute('data-v-') ||
                  node.querySelector && node.querySelector('[data-v-]') ||
                  node.className && node.className.includes('form-builder')
                )) {
                  console.log('Vue app content detected, showing form builder...');
                  observer.disconnect();
                  window.onFormBuilderReady();
                  return;
                }
              }
            }
          }
        });

        observer.observe(formBuilderElement, {
          childList: true,
          subtree: true
        });

        // Fallback timeout in case MutationObserver doesn't catch it
        setTimeout(() => {
          observer.disconnect();
          if (!formBuilderElement.classList.contains('initialized')) {
            console.log('MutationObserver timeout, showing form builder anyway...');
            window.onFormBuilderReady();
          }
        }, 3000);
      }

      return;
    }

    // If Vite form builder not ready, wait a bit more
    setTimeout(attemptInitialization, 100);
  }

  function showFallbackContent() {
    console.log('Showing fallback content...');

    // Check if Vue.js CDN is available for fallback
    if (typeof Vue !== 'undefined') {
      console.log('Vue.js CDN available, creating simple fallback...');

      try {
        const { createApp } = Vue;

        const app = createApp({
          data() {
            return {
              activeTab: 'fields',
              formData: window.formBuilderData?.formData || {},
              message: 'Form Builder (Fallback Mode)',
              availableFieldTypes: [
                {
                  id: 'text',
                  name: 'Text Input',
                  description: 'Single line text input',
                  icon: '📝',
                  color: 'blue'
                },
                {
                  id: 'textarea',
                  name: 'Text Area',
                  description: 'Multi-line text input',
                  icon: '📄',
                  color: 'green'
                },
                {
                  id: 'email',
                  name: 'Email',
                  description: 'Email address input',
                  icon: '📧',
                  color: 'purple'
                },
                {
                  id: 'select',
                  name: 'Dropdown',
                  description: 'Dropdown selection',
                  icon: '📋',
                  color: 'orange'
                }
              ]
            };
          },
          methods: {
            addField(fieldType) {
              console.log('Adding field:', fieldType);
              alert('Fallback mode: Field addition not implemented');
            }
          },
          mounted() {
            console.log('Fallback Form Builder Vue app mounted successfully');
          }
        });

        // Only mount if the element exists and isn't already mounted
        const element = document.getElementById('form-builder-app');
        if (element && !element.__vue_app__) {
          app.mount('#form-builder-app');

          // Enable visibility after mounting
          setTimeout(() => {
            element.classList.add('initialized', 'vue-mounted');

            // Hide loading state
            const loadingElement = document.getElementById('form-builder-loading');
            if (loadingElement) {
              loadingElement.style.display = 'none';
            }
          }, 100);

          console.log('Fallback Form Builder initialized');
        }

      } catch (error) {
        console.error('Error creating fallback Vue app:', error);
        showStaticFallback();
      }
    } else {
      showStaticFallback();
    }
  }

  function showStaticFallback() {
    console.log('Showing static fallback content...');
    const fallbackContent = document.getElementById('fallback-content');
    const formBuilderApp = document.getElementById('form-builder-app');
    const loadingElement = document.getElementById('form-builder-loading');

    // Hide loading state
    if (loadingElement) {
      loadingElement.style.display = 'none';
    }

    if (fallbackContent && formBuilderApp) {
      formBuilderApp.style.display = 'none';
      fallbackContent.classList.remove('hidden');
    }
  }

  // Start initialization when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', attemptInitialization);
  } else {
    // DOM already loaded
    attemptInitialization();
  }
</script>

<!-- Additional Form Builder Utilities -->
<script>
  // Global utilities for form builder
  window.FormBuilderUtils = {
    // Generate unique field ID
    generateFieldId() {
      return 'field_' + Math.random().toString(36).substr(2, 9);
    },
    
    // Validate field configuration
    validateField(field) {
      if (!field.name || !field.type) {
        return false;
      }
      return true;
    },
    
    // Format field for API
    formatFieldForAPI(field) {
      return {
        name: field.name,
        label: field.label || field.name,
        field_type: field.type,
        required: field.required || false,
        placeholder: field.placeholder || '',
        help_text: field.help_text || '',
        default_value: field.default_value || '',
        order: field.order || 0,
        is_visible: field.is_visible !== false,
        properties: field.properties || {},
        validation_rules: field.validation_rules || {},
        conditional_logic: field.conditional_logic || {}
      };
    },
    
    // Setup CSRF for AJAX requests
    setupCSRF() {
      const csrfToken = window.formBuilderData?.csrfToken;
      if (csrfToken && window.axios) {
        window.axios.defaults.headers.common['X-CSRFToken'] = csrfToken;
      }
    }
  };
  
  // Initialize CSRF setup
  window.FormBuilderUtils.setupCSRF();
  
  {% if debug %}
  console.log('Form Builder utilities loaded:', window.FormBuilderUtils);
  {% endif %}
</script>

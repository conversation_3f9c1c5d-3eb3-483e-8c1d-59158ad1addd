{% extends 'forms/base_form.html' %}
{% load static %}
{% load vite_tags %}

{% block title %}
  Form Builder - {{ form.name }} - PDFlex
{% endblock %}

{% block form_title %}
  {{ form.name }} - Form Builder
{% endblock %}
{% block form_subtitle %}
  Visual Form Designer • {{ form.status }} • {{ form.fields.count }} fields
{% endblock %}

{% block form_actions %}
  <a href="{% url 'forms:form_detail' form.slug %}" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
    </svg>Back to Form
  </a>
  <a href="{% url 'forms:form_edit' form.slug %}" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
    <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
    </svg>Settings
  </a>
{% endblock %}

{% block form_content %}
  <!-- Form Builder Loading State -->
  <div id="form-builder-loading" class="form-builder-loading">
    <div class="text-center">
      <div class="loading-spinner mx-auto mb-4"></div>
      <p class="text-gray-600">Loading Form Builder...</p>
      <p class="text-sm text-gray-500 mt-2">Initializing Vue.js components</p>
    </div>
  </div>

  <!-- Form Builder Vue App Container -->
  <div id="form-builder-app" class="flex-1 flex">
    <!-- Sidebar -->
    <div id="form-builder-sidebar" class="w-80 bg-white border-r border-gray-200 form-panel-scroll">
      {% include 'forms/partials/form_builder_sidebar.html' %}
    </div>

    <!-- Main Canvas Area -->
    <div class="flex-1 flex flex-col bg-gray-50">
      <!-- Canvas -->
      <div id="form-builder-canvas" class="flex-1 p-6 form-panel-scroll">
        {% include 'forms/partials/form_builder_canvas.html' %}
      </div>
    </div>

    <!-- Properties Panel -->
    <div id="form-builder-properties" class="w-80 bg-white border-l border-gray-200 form-panel-scroll">
      {% include 'forms/partials/form_builder_properties.html' %}
    </div>
  </div>
{% endblock %}

{% block fallback_title %}
  Form Builder
{% endblock %}
{% block fallback_message %}
  JavaScript is required to use the form builder interface.
{% endblock %}

{% block form_vue_scripts %}
  <!-- Vue.js CDN Fallback for development/fallback scenarios -->
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

  <!-- Include Form Builder JavaScript Components -->
  {% include 'forms/partials/form_builder_scripts.html' %}
{% endblock %}
